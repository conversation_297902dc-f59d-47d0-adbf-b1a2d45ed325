<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户历史订单查询</title>
    <link rel="stylesheet" href="/static/css/print-styles.css">
    <script src="/static/js/print-functions.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input, select, textarea { width: 100%; padding: 8px; box-sizing: border-box; }
        button { padding: 10px 15px; background: #007BFF; color: white; border: none; cursor: pointer; margin-right: 5px; }
        button:hover { background: #0056b3; }
        .customer-info { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }

        /* 订单表格样式 */
        .order-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            font-size: 14px;
        }
        .order-table th, .order-table td {
            padding: 10px;
            text-align: left;
            border: 1px solid #e0e0e0;
            vertical-align: middle;
        }
        .order-table th {
            background-color: #f2f7ff;
            font-weight: bold;
            color: #333;
            position: sticky;
            top: 0;
            box-shadow: 0 1px 0 #ddd;
        }
        .order-table tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .order-table tbody tr:hover {
            background-color: #e9f7fe;
        }
        .order-details-row td {
            padding: 0;
        }
        .order-details-container {
            padding: 10px;
            background-color: #f5f5f5;
        }
        .clothing-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            font-size: 13px;
        }
        .clothing-table th, .clothing-table td {
            padding: 6px 8px;
            border: 1px solid #ddd;
            font-size: 0.9em;
        }
        .clothing-table th {
            background-color: #e6f7ff;
        }
        .toggle-details {
            background: #007BFF;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
            transition: background 0.2s;
        }
        .toggle-details:hover {
            background: #0056b3;
        }

        .service-tag {
            display: inline-block;
            background: #007BFF;
            color: white;
            padding: 4px 8px;
            border-radius: 3px;
            margin-right: 5px;
            margin-bottom: 5px;
            font-size: 12px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        .service-tag.washing {
            background-color: #28a745;
        }
        .service-tag.darning {
            background-color: #17a2b8;
        }
        .service-tag.altering {
            background-color: #6f42c1;
        }
        .service-tag.other {
            background-color: #fd7e14;
        }
        .hidden { display: none; }
        .header { display: flex; justify-content: space-between; align-items: center; }
        .header a { text-decoration: none; color: #007BFF; }
        .payment-status {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
        .payment-status.paid {
            background: #28a745;
            color: white;
        }
        .payment-status.unpaid {
            background: #dc3545;
            color: white;
        }
        .pagination {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            align-items: center;
        }
        .pagination button {
            margin: 0 5px;
            background: #f5f5f5;
            color: #333;
        }
        .pagination button.active {
            background: #007BFF;
            color: white;
        }
        .pagination span {
            margin: 0 10px;
        }
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .order-total {
            font-size: 18px;
            font-weight: bold;
            color: #28a745;
        }
        .requirement-tag {
            background: #28a745;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            margin-right: 3px;
            font-size: 11px;
            display: inline-block;
        }

        /* 加载状态样式 */
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #007BFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 错误消息样式 */
        .error-message {
            padding: 10px 15px;
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            margin-bottom: 15px;
            display: none;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container { width: 95%; }
            .order-table th, .order-table td {
                padding: 5px;
                font-size: 0.9em;
            }
            .clothing-table th, .clothing-table td {
                padding: 4px;
                font-size: 0.8em;
            }
            /* 在小屏幕上隐藏一些列 */
            .order-table th:nth-child(2),
            .order-table td:nth-child(2) {
                display: none;
            }
            .order-table th:first-child,
            .order-table td:first-child {
                width: 40px;
            }
        }

        /* 打印样式优化 */
        @media print {
            body { font-size: 12px; }
            button, .customer-search, .header-controls { display: none !important; }
            .order-details-row { display: table-row !important; }
            .header h1 { font-size: 18px; }

            /* 打印时隐藏不需要的元素 */
            .modal {
                position: absolute !important;
                left: 0 !important;
                top: 0 !important;
                width: 100% !important;
                height: auto !important;
                overflow: visible !important;
                background-color: white !important;
                box-shadow: none !important;
                display: block !important;
            }
            .modal-content {
                box-shadow: none !important;
                width: 100% !important;
                margin: 0 !important;
                padding: 0 !important;
                animation: none !important;
            }
            .close-modal, .print-btn, .orderList, .pagination, .toggle-details {
                display: none !important;
            }

            /* 调整打印页边距 */
            @page {
                margin: 1cm;
            }
        }

        /* 头部控件样式 */
        .header-controls {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        .staff-info {
            display: flex;
            align-items: center;
            gap: 15px;
            background: #f5f5f5;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        .logout-btn {
            color: #dc3545;
            text-decoration: none;
            font-weight: 500;
        }
        .logout-btn:hover {
            text-decoration: underline;
        }
        /* 订单状态样式 */
        .order-status {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            color: white;
        }
        .status-pending {
            background-color: #ffc107;
            color: #212529;
        }
        .status-processing {
            background-color: #17a2b8;
        }
        .status-completed {
            background-color: #28a745;
        }
        .status-cancelled {
            background-color: #dc3545;
        }
        /* 新增状态样式 */
        .status-sorted {
            background-color: #6610f2;
        }
        .status-to-factory {
            background-color: #fd7e14;
        }
        .status-in-factory {
            background-color: #e83e8c;
        }
        .status-factory-sorted {
            background-color: #563d7c;
        }
        .status-out-factory {
            background-color: #0062cc;
        }
        .status-to-branch {
            background-color: #20c997;
        }
        .status-at-branch {
            background-color: #17a2b8;
        }
        .status-on-shelf {
            background-color: #6f42c1;
        }
        .status-delivering {
            background-color: #e83e8c;
        }
        .status-compensated {
            background-color: #adb5bd;
        }
        .status-self-picked {
            background-color: #28a745;
        }
        .status-delivered {
            background-color: #28a745;
        }

        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1050;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            overflow-y: auto;
        }
        .modal-content {
            position: relative;
            background-color: #fff;
            margin: 30px auto;
            padding: 20px;
            width: 90%;
            max-width: 800px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            animation: fadeIn 0.3s;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
            margin-bottom: 15px;
        }
        .modal-title {
            font-size: 1.2em;
            font-weight: bold;
            margin: 0;
        }
        .modal-actions {
            display: flex;
            gap: 10px;
        }
        .print-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 4px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .print-btn:hover {
            background: #218838;
        }
        .close-modal {
            background: none;
            border: none;
            font-size: 1.5em;
            cursor: pointer;
            padding: 0;
            color: #999;
        }
        .close-modal:hover {
            color: #333;
        }
        .modal-body {
            max-height: 70vh;
            overflow-y: auto;
            padding-right: 5px;
        }
        .modal-info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px 20px;
            margin-bottom: 15px;
        }
        .modal-info-item {
            display: flex;
        }
        .modal-info-label {
            font-weight: bold;
            margin-right: 5px;
            min-width: 80px;
        }
        #queryInfo {
            margin-bottom: 15px;
            padding: 10px 15px;
            background-color: #f2f7ff;
            border-radius: 5px;
            border-left: 4px solid #007BFF;
            font-size: 0.9em;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        #resultCount {
            margin-top: 5px;
            font-weight: bold;
            color: #007BFF;
        }









        /* 图片查看器样式 */
        .image-viewer-modal {
            z-index: 2000 !important;
        }

        .image-viewer-content {
            position: relative;
            background-color: #fff;
            margin: 5% auto;
            padding: 20px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            border-radius: 5px;
            overflow: auto;
            text-align: center;
        }

        #enlargedImage {
            max-width: 100%;
            max-height: 70vh;
            object-fit: contain;
        }

        /* 订单详情衣物卡片样式 */
        .clothing-items-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 20px;
        }

        .clothing-item-card {
            display: flex;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .clothing-item-info {
            flex: 1;
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .clothing-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }

        .clothing-item-header h5 {
            margin: 0;
            font-size: 16px;
        }

        .clothing-item-price {
            font-weight: bold;
            color: #e91e63;
        }

        .clothing-item-services {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .clothing-item-remarks {
            font-size: 13px;
            color: #666;
        }

        .clothing-item-photos {
            display: flex;
            padding: 10px;
            gap: 8px;
            background-color: #f9f9f9;
            align-items: center;
            flex-wrap: wrap;
            min-width: 120px;
        }

        .clothing-photo-container {
            width: 80px;
            height: 80px;
            overflow: hidden;
            border-radius: 4px;
            border: 1px solid #ddd;
            cursor: pointer;
        }

        .clothing-photo {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .clothing-photo:hover {
            transform: scale(1.05);
        }

        .no-photo {
            color: #999;
            font-style: italic;
            padding: 10px;
        }

        /* 订单状态更新表单样式 */
        .order-detail-status-form {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            border-left: 4px solid #007BFF;
        }

        .order-detail-status-controls {
            display: flex;
            gap: 15px;
            align-items: flex-end;
            flex-wrap: wrap;
        }

        .status-form-group {
            flex: 1;
            min-width: 150px;
        }

        .status-form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        .status-select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: white;
        }

        .update-status-btn {
            padding: 8px 16px;
            background-color: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            align-self: flex-end;
        }

        .update-status-btn:hover {
            background-color: #218838;
        }

        .action-button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }

        .action-button:hover {
            background-color: #0069d9;
        }

        @media (max-width: 768px) {
            .order-detail-status-controls {
                flex-direction: column;
            }

            .clothing-item-card {
                flex-direction: column;
            }
        }

        /* 衣物编辑模态框样式 */
        .edit-item-modal {
            z-index: 2100 !important;
        }

        .edit-form-group {
            margin-bottom: 15px;
        }

        .edit-form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .edit-form-group input,
        .edit-form-group textarea,
        .edit-form-group select {
            width: 100%;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .edit-form-services {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        .service-checkbox {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            margin-right: 15px;
        }

        .service-checkbox input {
            width: auto;
        }

        .requirements-section {
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            margin-top: 5px;
        }

        .save-edit-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }

        .edit-item-btn {
            background-color: #17a2b8;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            margin-left: 10px;
        }

        /* 编辑照片样式 */
        .edit-photo-gallery {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            background-color: #f9f9f9;
        }

        .edit-photo-thumbnails {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 10px;
            min-height: 40px;
        }

        .edit-photo-thumbnail-container {
            position: relative;
            width: 80px;
            height: 80px;
            border-radius: 4px;
            overflow: hidden;
            border: 1px solid #ddd;
        }

        .edit-photo-thumbnail {
            width: 100%;
            height: 100%;
            object-fit: cover;
            cursor: pointer;
        }

        .edit-remove-photo {
            position: absolute;
            top: 2px;
            right: 2px;
            background-color: rgba(255, 0, 0, 0.8);
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .edit-remove-photo:hover {
            background-color: rgba(255, 0, 0, 1);
        }

        .add-edit-photo-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
        }

        .add-edit-photo-btn:hover {
            background-color: #0056b3;
        }

        /* 摄像头模态框样式 */
        .camera-modal {
            display: none;
            position: fixed;
            z-index: 2200;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
        }

        .camera-modal-content {
            position: relative;
            margin: 5% auto;
            padding: 20px;
            width: 90%;
            max-width: 600px;
            background-color: white;
            border-radius: 8px;
            text-align: center;
        }

        .camera-feed {
            width: 100%;
            max-width: 500px;
            height: auto;
            border-radius: 8px;
            margin: 15px 0;
        }

        .camera-controls {
            margin-top: 20px;
        }

        .camera-controls button {
            margin: 0 10px;
            padding: 10px 20px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }

        #takePhotoBtn {
            background-color: #007bff;
            color: white;
        }

        #takePhotoBtn:hover {
            background-color: #0056b3;
        }

        #cancelBtn {
            background-color: #6c757d;
            color: white;
        }

        #cancelBtn:hover {
            background-color: #545b62;
        }

        @media print {
            .print-modal {
                display: none;
            }

            body * {
                visibility: hidden;
            }

            /* ... existing code ... */
        }

        /* 添加按钮悬停效果 */
        #selectAllVisible:hover {
            background-color: #5a6268 !important;
        }

        #clearSelection:hover {
            background-color: #c82333 !important;
        }

        #applyBatchUpdate:hover {
            background-color: #218838 !important;
            box-shadow: 0 2px 6px rgba(40,167,69,0.3) !important;
        }

        /* 下拉框悬停效果 */
        .status-select:hover {
            border-color: #adb5bd;
        }

        .status-select:focus {
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        }

        .service-badge.decoration {
            background-color: #eeeeff;
            border-color: #ccccff;
            color: #4444ff;
        }

        .label-remarks {
            font-size: 10px; /* 增大字体大小，从9px改为10px */
            color: #000; /* 修改颜色，从#444改为#000，使文字加黑 */
            margin-top: 1px; /* 减少上边距，从2px改为1px，使布局更紧凑 */
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
            line-height: 1.0; /* 减小行高，从1.1改为1.0，使布局更紧凑 */
            font-weight: bold; /* 添加字体加粗 */
        }

        /* 订单修改标记样式 */
        .modification-badge {
            display: inline-block;
            margin-left: 5px;
            font-size: 12px;
            color: #ff6b35;
            cursor: help;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Soulweave改衣坊订单历史</h1>
        <div class="header-controls">
            {% if session.staff_name %}
            <div class="staff-info">
                <span>营业员: {{ session.staff_name }}</span>
                <a href="/logout" class="logout-btn">登出</a>
            </div>
            {% endif %}
            <a href="/">返回主页</a>
        </div>
    </div>

    <!-- 优化查询组件布局，放在一行 -->
    <div class="customer-search">
        <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 15px; align-items: flex-end;">
            <div class="form-group" style="flex: 2; min-width: 200px; margin-bottom: 0;">
            <label for="customerPhone">客户手机号</label>
                <div style="display: flex; gap: 5px;">
                    <input type="tel" id="customerPhone" name="customerPhone" placeholder="输入手机号查询">
                    <button type="button" id="searchCustomer" style="white-space: nowrap;">查询</button>
            </div>
            </div>
            <div class="form-group" style="flex: 2; min-width: 200px; margin-bottom: 0;">
                <label for="customerName">客户姓名</label>
                <div style="display: flex; gap: 5px;">
                    <input type="text" id="customerName" name="customerName" placeholder="输入姓名查询">
                </div>
            </div>
            <div class="form-group" style="flex: 1.5; min-width: 180px; margin-bottom: 0;">
                <label for="orderNumber">订单号</label>
                <div style="display: flex; gap: 5px;">
                    <input type="text" id="orderNumber" name="orderNumber" placeholder="输入订单号查询">
                </div>
            </div>
            <div class="form-group" style="flex: 1.5; min-width: 150px; margin-bottom: 0;">
                <label for="orderStatus">订单状态</label>
                <select id="orderStatus" name="orderStatus">
                    <option value="">所有状态</option>
                    <option value="门店已分拣">门店已分拣</option>
                    <option value="送至工厂中">送至工厂中</option>
                    <option value="入厂">入厂</option>
                    <option value="工厂分拣">工厂分拣</option>
                    <option value="出厂">出厂</option>
                    <option value="送至分店中">送至分店中</option>
                    <option value="已送至分店">已送至分店</option>
                    <option value="已上架">已上架</option>
                    <option value="配送中">配送中</option>
                    <option value="已取消">已取消</option>
                    <option value="已退赔">已退赔</option>
                    <option value="已自取">已自取</option>
                    <option value="已配送">已配送</option>
                </select>
            </div>
            <div class="form-group" style="flex: 1; min-width: 150px; margin-bottom: 0;">
                <label for="orderDate">订单日期</label>
                <input type="date" id="orderDate" name="orderDate">
            </div>
            <div class="form-group" style="flex: 1; min-width: 120px; margin-bottom: 0;">
                <label for="operatorFilter">营业员</label>
                <select id="operatorFilter" name="operatorFilter">
                    <option value="">所有营业员</option>
                </select>
            </div>
            <div class="form-group" style="flex: 1; min-width: 120px; margin-bottom: 0;">
                <label for="paymentMethodFilter">支付方式</label>
                <select id="paymentMethodFilter" name="paymentMethodFilter">
                    <option value="">所有支付方式</option>
                    <option value="未付款">未付款</option>
                    <option value="扫银联码">扫银联码</option>
                    <option value="商场POS">商场POS</option>
                    <option value="余额">余额支付</option>
                    <option value="月结">月结</option>
                </select>
            </div>
            <div style="display: flex; gap: 10px;">
                <button type="button" id="searchByFilter" style="background-color: #28a745; height: 38px; white-space: nowrap; border: none; border-radius: 4px; color: white; padding: 0 15px; font-weight: 500; cursor: pointer;">
                    <i class="fa fa-search" style="margin-right: 5px;"></i>筛选查询
                </button>
                <button type="button" id="clearFilters" style="background-color: #6c757d; height: 38px; white-space: nowrap; border: none; border-radius: 4px; color: white; padding: 0 15px; font-weight: 500; cursor: pointer;">
                    <i class="fa fa-times" style="margin-right: 5px;"></i>清除筛选
                </button>
            </div>
        </div>
    </div>

    <!-- 优化查询结果信息布局 -->
    <div id="customerData" class="hidden">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h2 style="margin: 0;">订单查询结果</h2>
            <div id="queryInfo" style="display: flex; gap: 15px; align-items: center; background-color: #f2f7ff; padding: 8px 15px; border-radius: 5px; border-left: 4px solid #007BFF; margin: 0; flex-wrap: nowrap;">
                <div id="queryConditions" style="margin: 0;"></div>
                <div style="width: 1px; height: 20px; background-color: #ccc;"></div>
                <div id="resultCount" style="margin: 0; font-weight: bold; color: #007BFF;"></div>
            </div>
        </div>

        <!-- 添加权限提示信息 -->
        {% if session.get('staff_role') != 'manager' %}
        <div style="margin-bottom: 15px; background-color: #fff3cd; padding: 10px; border-radius: 8px; border-left: 4px solid #ffc107;">
            <strong>提示：</strong> 您当前查看的是由您操作的订单。普通营业员只能查看自己操作的订单。
        </div>
        {% else %}
        <div style="margin-bottom: 15px; background-color: #d4edda; padding: 10px; border-radius: 8px; border-left: 4px solid #28a745;">
            <strong>提示：</strong> 您具有管理员权限，可以查看所有订单。
        </div>
        {% endif %}

        <!-- 优化批量操作区域布局 - 单行紧凑版 -->
        <div id="batchOperations" class="hidden" style="margin: 15px 0; background-color: #f8f9fa; padding: 10px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); border-left: 4px solid #007BFF;">
            <div style="display: flex; align-items: center; flex-wrap: nowrap; gap: 10px; overflow-x: auto;">
                <h3 style="margin: 0; white-space: nowrap; color: #3a3a3a; font-size: 14px; display: flex; align-items: center; padding-right: 10px; border-right: 1px solid #dee2e6;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" style="margin-right: 5px; fill: #007BFF;">
                        <path d="M3 17h18v2H3v-2zm0-7h18v2H3v-2zm0-7h18v2H3V3z"/>
                    </svg>
                    批量操作
                </h3>

                <div style="display: flex; align-items: center; gap: 8px; flex: 1;">
                    <div class="form-group" style="margin: 0; flex: 0 0 auto; display: flex; align-items: center; gap: 5px;">
                        <label for="batchStatus" style="margin: 0; font-size: 13px; color: #495057; white-space: nowrap;">状态:</label>
                        <select id="batchStatus" class="status-select" style="border-radius: 4px; border: 1px solid #ced4da; padding: 4px 8px; font-size: 13px; max-width: 120px;">
                            <option value="">不修改</option>
                            <option value="门店已分拣">门店已分拣</option>
                            <option value="送至工厂中">送至工厂中</option>
                            <option value="入厂">入厂</option>
                            <option value="工厂分拣">工厂分拣</option>
                            <option value="出厂">出厂</option>
                            <option value="送至分店中">送至分店中</option>
                            <option value="已送至分店">已送至分店</option>
                            <option value="已上架">已上架</option>
                            <option value="配送中">配送中</option>
                            <option value="已取消">已取消</option>
                            <option value="已退赔">已退赔</option>
                            <option value="已自取">已自取</option>
                            <option value="已配送">已配送</option>
                        </select>
                    </div>

                    <div class="form-group" style="margin: 0; flex: 0 0 auto; display: flex; align-items: center; gap: 5px;">
                        <label for="batchPaymentStatus" style="margin: 0; font-size: 13px; color: #495057; white-space: nowrap;">支付状态:</label>
                        <select id="batchPaymentStatus" class="status-select" style="border-radius: 4px; border: 1px solid #ced4da; padding: 4px 8px; font-size: 13px; max-width: 120px;">
                            <option value="">不修改</option>
                            <option value="已付款">已付款</option>
                            <option value="未付款">未付款</option>
                        </select>
                    </div>

                    <div class="form-group" style="margin: 0; flex: 1 0 auto; display: flex; align-items: center; gap: 5px;">
                        <label for="batchRemarks" style="margin: 0; font-size: 13px; color: #495057; white-space: nowrap;">备注:</label>
                        <input type="text" id="batchRemarks" style="border-radius: 4px; border: 1px solid #ced4da; padding: 4px 8px; font-size: 13px; flex: 1;" placeholder="批量状态变更备注">
                    </div>

                    <button type="button" id="applyBatchUpdate" style="background-color: #28a745; height: 30px; border: none; border-radius: 4px; color: white; padding: 0 10px; font-size: 13px; font-weight: 500; cursor: pointer; white-space: nowrap;">
                        应用更改
                    </button>
                </div>

                <div style="display: flex; align-items: center; gap: 8px; flex: 0 0 auto; background-color: #e9f7fe; padding: 5px 10px; border-radius: 6px; margin-left: auto;">
                    <span id="selectedCount" style="font-weight: 500; color: #007BFF; white-space: nowrap; font-size: 13px;">已选择 0 项</span>
                    <div style="display: flex; gap: 5px;">
                        <button type="button" id="selectAllVisible" style="background-color: #6c757d; white-space: nowrap; border: none; border-radius: 4px; color: white; padding: 3px 8px; font-size: 12px; cursor: pointer;">选择本页</button>
                        <button type="button" id="clearSelection" style="background-color: #dc3545; white-space: nowrap; border: none; border-radius: 4px; color: white; padding: 3px 8px; font-size: 12px; cursor: pointer;">清除选择</button>
                    </div>
                </div>
            </div>
        </div>

        <div id="orderList"></div>

        <!-- 移除分页控件 -->
    </div>

    <div id="noResults" class="hidden">
        <p>未找到客户信息，请确认手机号是否正确。</p>
    </div>

    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="errorMessageContainer" class="error-message">
        <p></p>
    </div>

    <!-- 订单详情模态框 -->
    <div id="orderDetailModal" class="modal">
        <div class="modal-content">
            <span id="closeModal" class="close-modal">&times;</span>
            <h3>订单详情</h3>
            <div id="modalContent">
                <!-- 订单详情内容将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 图片查看器弹窗 -->
    <div id="imageViewerModal" class="modal image-viewer-modal">
        <div class="image-viewer-content">
            <span id="closeImageViewer" class="close-modal">&times;</span>
            <img id="enlargedImage" src="" alt="放大图片">
        </div>
    </div>

    <!-- 衣物编辑弹窗 -->
    <div id="editItemModal" class="modal edit-item-modal">
        <div class="modal-content">
            <span id="closeEditModal" class="close-modal">&times;</span>
            <h3>编辑衣物信息</h3>
            <div id="editItemForm">
                <input type="hidden" id="editItemId">
                <input type="hidden" id="editOrderId">

                <div class="edit-form-group">
                    <label for="editItemName">衣物名称</label>
                    <input type="text" id="editItemName">
                </div>

                <div class="edit-form-group">
                    <label for="editItemColor">颜色</label>
                    <input type="text" id="editItemColor">
                </div>

                <div class="edit-form-group">
                    <label for="editItemPrice">洗衣价格</label>
                    <input type="number" id="editItemPrice" step="0.01" min="0">
                </div>

                <div class="edit-form-group">
                    <label>服务类型</label>
                    <div class="edit-form-services">
                        <label class="service-checkbox">
                            <input type="checkbox" id="serviceWashing" value="洗衣"> 洗衣
                        </label>
                        <label class="service-checkbox">
                            <input type="checkbox" id="serviceDarning" value="织补"> 织补
                        </label>
                        <label class="service-checkbox">
                            <input type="checkbox" id="serviceAltering" value="改衣"> 改衣
                        </label>
                        <label class="service-checkbox">
                            <input type="checkbox" id="serviceOther" value="其他"> 其他
                        </label>
                        <label class="service-checkbox">
                            <input type="checkbox" id="serviceUrgent" value="加急"> 加急
                        </label>
                    </div>
                </div>

                <div id="darningSection" class="requirements-section" style="display: none;">
                    <div class="edit-form-group">
                        <label for="editDarningRequirement">织补要求</label>
                        <textarea id="editDarningRequirement" rows="2"></textarea>
                    </div>
                    <div class="edit-form-group">
                        <label for="editDarningPrice">织补价格</label>
                        <input type="number" id="editDarningPrice" step="0.01" min="0" value="20">
                    </div>
                </div>

                <div id="alteringSection" class="requirements-section" style="display: none;">
                    <div class="edit-form-group">
                        <label for="editAlteringRequirement">改衣要求</label>
                        <textarea id="editAlteringRequirement" rows="2"></textarea>
                    </div>
                    <div class="edit-form-group">
                        <label for="editAlteringPrice">改衣价格</label>
                        <input type="number" id="editAlteringPrice" step="0.01" min="0" value="30">
                    </div>
                </div>

                <div id="otherSection" class="requirements-section" style="display: none;">
                    <div class="edit-form-group">
                        <label for="editOtherRequirement">其他服务要求</label>
                        <textarea id="editOtherRequirement" rows="2" placeholder="请描述具体的服务内容"></textarea>
                    </div>
                    <div class="edit-form-group">
                        <label for="editOtherPrice">其他服务价格</label>
                        <input type="number" id="editOtherPrice" step="0.01" min="0" value="20">
                    </div>
                </div>

                <div class="edit-form-group">
                    <label for="editItemFlaw">瑕疵描述</label>
                    <textarea id="editItemFlaw" rows="2" placeholder="描述衣物瑕疵信息（可选）"></textarea>
                </div>

                <div class="edit-form-group">
                    <label for="editItemRemarks">备注</label>
                    <textarea id="editItemRemarks" rows="2"></textarea>
                </div>

                <div class="edit-form-group">
                    <label>衣物照片</label>
                    <div class="edit-photo-gallery">
                        <div id="editPhotoThumbnails" class="edit-photo-thumbnails"></div>
                        <button type="button" id="addEditPhotoBtn" class="add-edit-photo-btn">添加照片</button>
                    </div>
                </div>

                <button id="saveItemBtn" class="save-edit-btn">保存修改</button>
            </div>
        </div>
    </div>

    <!-- 小票打印弹窗 -->
    <div id="receiptModal" class="print-modal" style="display: none;">
        <div class="print-modal-content">
            <div class="print-modal-header">
                <h3>小票打印预览</h3>
                <button class="close-print-modal">&times;</button>
            </div>
            <div class="print-modal-body" id="receiptContent">
                <!-- 小票内容将在这里动态生成 -->
            </div>
            <div class="print-modal-footer">
                <button id="printReceiptBtn" class="print-action-btn">打印小票</button>
            </div>
        </div>
    </div>

    <!-- 水洗唛打印弹窗 -->
    <div id="labelModal" class="print-modal" style="display: none;">
        <div class="print-modal-content">
            <div class="print-modal-header">
                <h3>水洗唛打印预览</h3>
                <button class="close-print-modal">&times;</button>
            </div>
            <div class="label-selection-area">
                <span class="label-select-title">选择要打印的水洗唛：</span>
                <select id="labelSelect">
                    <option value="all">打印所有水洗唛</option>
                    <!-- 衣物选项将在这里动态生成 -->
                </select>
                <button id="printAllLabelsBtn" class="print-all-labels">打印全部</button>
                <button id="printSelectedLabelBtn" class="print-selected-label">打印选中项</button>
            </div>
            <div class="print-modal-body" id="labelContent">
                <!-- 水洗唛内容将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 摄像头弹窗 -->
    <div id="cameraModal" class="camera-modal" style="display: none;">
        <div class="camera-modal-content">
            <span class="close-modal">&times;</span>
            <h3>拍摄衣物照片</h3>
            <video id="modalCameraFeed" class="camera-feed" autoplay></video>
            <canvas id="photoCanvas" style="display:none;"></canvas>
            <div class="camera-controls">
                <button id="takePhotoBtn" type="button">拍照</button>
                <button id="cancelBtn" type="button">取消</button>
            </div>
        </div>
    </div>

    <script src="/static/js/clothing-options.js"></script>
    <script src="/static/js/print-functions.js"></script>
    <script src="/static/js/image-compressor.js"></script>
    <script>
        let paginationData = {
            phone: '',
            name: '',
            date: '',
            orderNumber: '',
            orderStatus: ''
        };

        // 存储已选择的订单ID
        let selectedOrderIds = new Set();

        // 检查是否正在加载
        function isLoading() {
            return document.getElementById('loadingOverlay').style.display === 'flex';
        }

        // 查询客户历史
        async function fetchCustomerHistory() {
            try {
                const phone = document.getElementById('customerPhone').value;
                const name = document.getElementById('customerName').value;
                const date = document.getElementById('orderDate').value;
                const orderNumber = document.getElementById('orderNumber').value;
                const orderStatus = document.getElementById('orderStatus').value;
                const operator = document.getElementById('operatorFilter').value;
                const paymentMethod = document.getElementById('paymentMethodFilter').value;

                // 更新查询数据
                paginationData.phone = phone;
                paginationData.name = name;
                paginationData.date = date;
                paginationData.orderNumber = orderNumber;
                paginationData.orderStatus = orderStatus;
                paginationData.operator = operator;
                paginationData.paymentMethod = paymentMethod;

                showLoading(true);
                showError(''); // 清除之前的错误信息

                // 构建查询参数
                let queryParams = `per_page=500`; // 设置较大的每页记录数，基本相当于不分页

                // 判断是否有筛选条件
                let hasFilter = false;

                // 仅当提供了手机号或姓名或日期时才添加这些参数
                if (phone) {
                    queryParams += `&phone=${encodeURIComponent(phone)}`;
                    hasFilter = true;
                }
                if (name) {
                    queryParams += `&name=${encodeURIComponent(name)}`;
                    hasFilter = true;
                }
                if (date) {
                    queryParams += `&date=${encodeURIComponent(date)}`;
                    hasFilter = true;
                }
                if (orderNumber) {
                    queryParams += `&order_number=${encodeURIComponent(orderNumber)}`;
                    hasFilter = true;
                }
                if (orderStatus) {
                    queryParams += `&status=${encodeURIComponent(orderStatus)}`;
                    hasFilter = true;
                }
                if (operator) {
                    queryParams += `&operator=${encodeURIComponent(operator)}`;
                    hasFilter = true;
                }
                if (paymentMethod) {
                    queryParams += `&payment_method=${encodeURIComponent(paymentMethod)}`;
                    hasFilter = true;
                }

                // 如果没有任何筛选条件，添加all=true参数表示获取所有订单
                if (!hasFilter) {
                    queryParams += '&all=true';
                }

                const response = await fetch(`/customer_history?${queryParams}`, {
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                // 检查是否重定向到登录页面
                if (response.redirected || response.url.includes('/login')) {
                    showError('会话已过期，请重新登录');
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 2000);
                    return;
                }

                // 检查响应状态
                if (!response.ok) {
                    if (response.status === 401 || response.status === 403) {
                        showError('无权限或会话已过期，请重新登录');
                        setTimeout(() => {
                            window.location.href = '/login';
                        }, 2000);
                        return;
                    }
                    throw new Error(`服务器错误: ${response.status}`);
                }

                // 检查内容类型
                const contentType = response.headers.get("content-type");
                if (!contentType || !contentType.includes("application/json")) {
                    showError('服务器返回了非JSON数据，可能需要重新登录');
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 2000);
                    return;
                }

                const data = await response.json();

                if (data.found) {
                    // 显示查询条件信息
                    let conditionsText = '查询条件：';
                    let hasCondition = false;

                    if (phone) {
                        conditionsText += `电话号码 "${phone}"`;
                        hasCondition = true;
                    }

                    if (name) {
                        conditionsText += hasCondition ? `, 客户姓名 "${name}"` : `客户姓名 "${name}"`;
                        hasCondition = true;
                    }

                    if (date) {
                        const formattedDate = new Date(date).toLocaleDateString('zh-CN');
                        conditionsText += hasCondition ? `, 订单日期 "${formattedDate}"` : `订单日期 "${formattedDate}"`;
                        hasCondition = true;
                    }

                    if (orderNumber) {
                        conditionsText += hasCondition ? `, 订单号 "${orderNumber}"` : `订单号 "${orderNumber}"`;
                        hasCondition = true;
                    }

                    if (orderStatus) {
                        conditionsText += hasCondition ? `, 订单状态 "${orderStatus}"` : `订单状态 "${orderStatus}"`;
                        hasCondition = true;
                    }

                    if (operator) {
                        conditionsText += hasCondition ? `, 营业员 "${operator}"` : `营业员 "${operator}"`;
                        hasCondition = true;
                    }

                    if (paymentMethod) {
                        conditionsText += hasCondition ? `, 支付方式 "${paymentMethod}"` : `支付方式 "${paymentMethod}"`;
                        hasCondition = true;
                    }

                    if (!hasCondition) {
                        conditionsText += '全部订单';
                    }

                    document.getElementById('queryConditions').textContent = conditionsText;
                    document.getElementById('resultCount').textContent = `共找到 ${data.pagination.total} 条记录`;

                    // 显示订单列表
                    const orderList = document.getElementById('orderList');
                    orderList.innerHTML = '';

                    if (data.orders.length === 0) {
                        orderList.innerHTML = '<p>暂无历史订单记录</p>';
                        document.getElementById('batchOperations').classList.add('hidden');
                    } else {
                        // 显示批量操作区域
                        document.getElementById('batchOperations').classList.remove('hidden');

                        // 计算汇总信息
                        let totalOrders = data.orders.length;
                        let totalAmount = 0;
                        let totalItems = 0;
                        data.orders.forEach(order => {
                            totalAmount += order.total_amount;
                            // 修复：使用数量总和而不是条目数
                            totalItems += order.clothes.reduce((sum, item) => sum + (item.quantity || 1), 0);
                        });

                        // 添加汇总信息区域和表格
                        let contentHtml = `
                            <div style="margin-bottom: 15px; padding: 12px 15px; background-color: #f8f9fa; border-radius: 6px; border-left: 4px solid #28a745; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                <div style="display: flex; flex-wrap: wrap; gap: 30px; align-items: center;">
                                    <div style="display: flex; flex-direction: column; align-items: center;">
                                        <span style="font-size: 13px; color: #666;">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" style="margin-right: 5px; fill: #666; display: inline-block; vertical-align: -2px;">
                                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zm-1 2l5 5h-5V4zM6 20V4h5v7h7v9H6z"/>
                                            </svg>订单数量
                                        </span>
                                        <span style="font-size: 20px; font-weight: bold; color: #28a745;">${totalOrders}</span>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center;">
                                        <span style="font-size: 13px; color: #666;">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" style="margin-right: 5px; fill: #666; display: inline-block; vertical-align: -2px;">
                                                <path d="M12 4c-4.42 0-8 3.58-8 8s3.58 8 8 8 8-3.58 8-8-3.58-8-8-8zm1 13h-2v-6h2v6zm0-8h-2V7h2v2z"/>
                                            </svg>衣物总数
                                        </span>
                                        <span style="font-size: 20px; font-weight: bold; color: #28a745;">${totalItems}</span>
                                    </div>
                                    <div style="display: flex; flex-direction: column; align-items: center;">
                                        <span style="font-size: 13px; color: #666;">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" style="margin-right: 5px; fill: #666; display: inline-block; vertical-align: -2px;">
                                                <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
                                            </svg>订单总金额
                                        </span>
                                        <span style="font-size: 20px; font-weight: bold; color: #28a745;">¥${totalAmount.toFixed(2)}</span>
                                    </div>
                                </div>
                            </div>

                            <table class="order-table">
                                <thead>
                                    <tr>
                                        <th width="40"><input type="checkbox" id="selectAll"></th>
                                        <th width="60">订单编号</th>
                                        <th width="130">订单流水号</th>
                                        <th width="100">创建日期</th>
                                        <th width="80">会员姓名</th>
                                        <th width="120">衣物名称</th>
                                        <th width="60">衣物数量</th>
                                        <th width="80">订单金额</th>
                                        <th width="80">订单状态</th>
                                        <th width="90">支付方式</th>
                                        <th width="100">支付时间</th>
                                        <th width="80">营业员</th>
                                        <th width="60">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                        `;

                        data.orders.forEach((order, index) => {
                            const paymentStatusClass = order.payment_status === '已付款' ? 'paid' : 'unpaid';
                            const isSelected = selectedOrderIds.has(order.id.toString());

                            // 获取衣物名称列表
                            let clothesNames = order.clothes.map(item => item.name).join(', ');
                            // 如果衣物名称过长，截断并添加省略号
                            if (clothesNames.length > 30) {
                                clothesNames = clothesNames.substring(0, 30) + '...';
                            }

                            // 订单行
                            contentHtml += `
                                <tr>
                                    <td><input type="checkbox" class="order-checkbox" data-id="${order.id}" ${isSelected ? 'checked' : ''}></td>
                                    <td>
                                        ${order.id}
                                        ${order.is_modified ? '<span class="modification-badge" title="此订单已被修改过">✏️</span>' : ''}
                                    </td>
                                    <td>${order.order_number}</td>
                                    <td>${order.date}</td>
                                    <td>${order.customer_name}</td>
                                    <td title="${order.clothes.map(item => item.name).join(', ')}" style="max-width: 120px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${clothesNames}</td>
                                    <td>${order.clothes.reduce((sum, item) => sum + (item.quantity || 1), 0)}</td>
                                    <td>¥${order.total_amount.toFixed(2)}</td>
                                    <td><span class="order-status status-${getOrderStatusClass(order.status)}">${order.status}</span></td>
                                    <td>
                                        ${order.payment_method}
                                        <span class="payment-status ${paymentStatusClass}">${order.payment_status}</span>
                                    </td>
                                    <td>${order.payment_time || '-'}</td>
                                    <td>${order.operator || '未知'}</td>
                                    <td>
                                        <button class="toggle-details" data-index="${index}">详情</button>
                                    </td>
                                </tr>
                            `;
                        });

                        // 结束表格
                        contentHtml += `
                                </tbody>
                            </table>
                        `;

                        orderList.innerHTML = contentHtml;

                        // 添加详情按钮点击事件
                        document.querySelectorAll('.toggle-details').forEach((button, index) => {
                            button.addEventListener('click', function() {
                                // 获取对应的订单数据
                                const order = data.orders[index];
                                // 添加客户信息到订单对象
                                order.customerInfo = {
                                    name: data.customer.name,
                                    phone: data.customer.phone
                                };
                                showOrderDetail(order);
                            });
                        });

                        // 添加复选框事件处理
                        setupCheckboxHandlers();
                    }

                    document.getElementById('customerData').classList.remove('hidden');
                    document.getElementById('noResults').classList.add('hidden');
                } else {
                    document.getElementById('customerData').classList.add('hidden');
                    document.getElementById('noResults').classList.remove('hidden');
                    document.getElementById('batchOperations').classList.add('hidden');
                }
            } catch (error) {
                console.error('查询错误:', error);
                showError('查询失败: ' + error.message);
                document.getElementById('customerData').classList.add('hidden');
                document.getElementById('noResults').classList.add('hidden');
                document.getElementById('batchOperations').classList.add('hidden');
            } finally {
                showLoading(false);
            }
        }

        // 配置复选框相关事件处理
        function setupCheckboxHandlers() {
            // 全选框事件
            const selectAllCheckbox = document.getElementById('selectAll');
            selectAllCheckbox.addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.order-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = selectAllCheckbox.checked;
                    const orderId = checkbox.getAttribute('data-id');
                    if (selectAllCheckbox.checked) {
                        selectedOrderIds.add(orderId);
                    } else {
                        selectedOrderIds.delete(orderId);
                    }
                });
                updateSelectedCount();
            });

            // 各订单复选框事件
            document.querySelectorAll('.order-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const orderId = this.getAttribute('data-id');
                    if (this.checked) {
                        selectedOrderIds.add(orderId);
                    } else {
                        selectedOrderIds.delete(orderId);
                        // 如果取消选中某个项，全选框也应取消选中
                        document.getElementById('selectAll').checked = false;
                    }
                    updateSelectedCount();
                });
            });

            // 更新已选数量显示
            updateSelectedCount();
        }

        // 更新已选数量显示
        function updateSelectedCount() {
            document.getElementById('selectedCount').textContent = `已选择 ${selectedOrderIds.size} 项`;
        }

        // 批量更新订单状态
        async function batchUpdateOrderStatus() {
            if (selectedOrderIds.size === 0) {
                alert('请至少选择一个订单');
                return;
            }

            const newStatus = document.getElementById('batchStatus').value;
            const newPaymentStatus = document.getElementById('batchPaymentStatus').value;
            const remarks = document.getElementById('batchRemarks').value;

            if (!newStatus && !newPaymentStatus) {
                alert('请至少选择一种要修改的状态');
                return;
            }

            try {
                showLoading(true);

                const response = await fetch('/batch_update_order_status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        order_ids: Array.from(selectedOrderIds),
                        status: newStatus || null,
                        payment_status: newPaymentStatus || null,
                        remarks: remarks
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || '批量更新订单状态失败');
                }

                const result = await response.json();

                if (result.success) {
                    let message = `成功更新 ${result.updated_count} 个订单`;
                    if (result.invalid_count > 0) {
                        message += `，${result.invalid_count} 个订单因状态流转限制未更新`;
                    }
                    alert(message);
                    // 清空选择
                    selectedOrderIds.clear();
                    // 重新加载数据
                    fetchCustomerHistory();
                } else {
                    throw new Error(result.error || '批量更新订单状态失败');
                }

            } catch (error) {
                console.error('批量更新订单状态错误:', error);
                alert('批量更新订单状态失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 按钮点击事件
        document.getElementById('searchCustomer').addEventListener('click', function() {
            const phone = document.getElementById('customerPhone').value;
            if (!phone) {
                showError('请输入手机号');
                return;
            }

            document.getElementById('customerName').value = '';
            document.getElementById('orderDate').value = '';

            fetchCustomerHistory();
        });

        document.getElementById('searchByFilter').addEventListener('click', function() {
            fetchCustomerHistory();
        });

        // 为订单号和订单状态添加查询触发机制
        document.getElementById('orderNumber').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                document.getElementById('searchByFilter').click();
            }
        });

        document.getElementById('orderStatus').addEventListener('change', function() {
            // 等待下拉菜单值变更后再查询
            setTimeout(function() {
                document.getElementById('searchByFilter').click();
            }, 100);
        });

        // 修改姓名搜索按钮的文本，使其更通用
        document.getElementById('searchByFilter').innerText = '筛选查询';

        document.getElementById('clearFilters').addEventListener('click', function() {
            document.getElementById('customerPhone').value = '';
            document.getElementById('customerName').value = '';
            document.getElementById('orderDate').value = '';
            document.getElementById('orderNumber').value = '';
            document.getElementById('orderStatus').value = '';
            document.getElementById('operatorFilter').value = '';
            document.getElementById('paymentMethodFilter').value = '';
            showError('');

            // 重置查询数据并加载所有订单
            paginationData.phone = '';
            paginationData.name = '';
            paginationData.date = '';
            paginationData.orderNumber = '';
            paginationData.orderStatus = '';
            paginationData.operator = '';
            paginationData.paymentMethod = '';
            fetchCustomerHistory();
        });

        // 选择本页按钮事件
        document.getElementById('selectAllVisible').addEventListener('click', function() {
            const selectAllCheckbox = document.getElementById('selectAll');
            selectAllCheckbox.checked = true;
            const checkboxes = document.querySelectorAll('.order-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
                const orderId = checkbox.getAttribute('data-id');
                selectedOrderIds.add(orderId);
            });
            updateSelectedCount();
        });

        // 清除选择按钮事件
        document.getElementById('clearSelection').addEventListener('click', function() {
            selectedOrderIds.clear();
            const selectAllCheckbox = document.getElementById('selectAll');
            selectAllCheckbox.checked = false;
            const checkboxes = document.querySelectorAll('.order-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            updateSelectedCount();
        });

        // 应用批量更新按钮事件
        document.getElementById('applyBatchUpdate').addEventListener('click', function() {
            batchUpdateOrderStatus();
        });

        // 在页面加载完成后执行初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 加载营业员列表
            loadOperatorList();

            // 检查URL参数，如果有订单号参数则自动填充并搜索
            const urlParams = new URLSearchParams(window.location.search);
            const orderNumber = urlParams.get('order_number');

            if (orderNumber) {
                // 填充订单号输入框
                document.getElementById('orderNumber').value = orderNumber;
                // 自动执行搜索
                fetchCustomerHistory();
            } else {
                // 页面加载时自动查询所有订单
                fetchCustomerHistory();
            }

            // 添加图片点击事件委托
            document.addEventListener('click', function(e) {
                if (e.target && e.target.classList.contains('clothing-photo')) {
                    const imageSrc = e.target.getAttribute('data-image-src');
                    if (imageSrc) {
                        showEnlargedImage(imageSrc);
                    }
                }
            });

            // 图片查看器关闭按钮事件
            const closeImageViewer = document.getElementById('closeImageViewer');
            const imageViewerModal = document.getElementById('imageViewerModal');

            if (closeImageViewer && imageViewerModal) {
                closeImageViewer.onclick = function() {
                    imageViewerModal.style.display = 'none';
                }

                // 点击模态框外部区域关闭模态框
                window.onclick = function(event) {
                    if (event.target == imageViewerModal) {
                        imageViewerModal.style.display = 'none';
                    }
                }
            }

            // 手机号输入框回车事件
            const phoneInput = document.getElementById('customerPhone');
            phoneInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    document.getElementById('searchCustomer').click();
                }
            });

            // 姓名输入框回车事件
            const nameInput = document.getElementById('customerName');
            nameInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    document.getElementById('searchByFilter').click();
                }
            });

            // 日期选择器变更事件修改为触发searchByFilter按钮
            const dateInput = document.getElementById('orderDate');
            dateInput.addEventListener('change', function() {
                document.getElementById('searchByFilter').click();
            });

            // 关闭弹窗
            document.getElementById('closeModal').addEventListener('click', function() {
                document.getElementById('orderDetailModal').style.display = 'none';
            });

            // 关闭打印弹窗
            document.querySelectorAll('.close-print-modal').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.getElementById('receiptModal').style.display = 'none';
                    document.getElementById('labelModal').style.display = 'none';
                });
            });

            // 关闭编辑衣物模态框
            document.getElementById('closeEditModal').addEventListener('click', function() {
                document.getElementById('editItemModal').style.display = 'none';
            });

            // 织补服务复选框变更事件
            document.getElementById('serviceDarning').addEventListener('change', function() {
                document.getElementById('darningSection').style.display = this.checked ? 'block' : 'none';
            });

            // 改衣服务复选框变更事件
            document.getElementById('serviceAltering').addEventListener('change', function() {
                document.getElementById('alteringSection').style.display = this.checked ? 'block' : 'none';
            });

            // 其他服务复选框变更事件
            document.getElementById('serviceOther').addEventListener('change', function() {
                document.getElementById('otherSection').style.display = this.checked ? 'block' : 'none';
            });

            // 保存编辑按钮事件
            document.getElementById('saveItemBtn').addEventListener('click', saveItemEdit);

            // 点击模态框外部关闭
            window.addEventListener('click', function(event) {
                const editItemModal = document.getElementById('editItemModal');
                if (event.target === editItemModal) {
                    editItemModal.style.display = 'none';
                }
            });
        });

        // 加载营业员列表
        async function loadOperatorList() {
            try {
                const response = await fetch('/api/staff_list', {
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    const operatorSelect = document.getElementById('operatorFilter');

                    // 清空现有选项（保留"所有营业员"选项）
                    operatorSelect.innerHTML = '<option value="">所有营业员</option>';

                    // 添加营业员选项
                    data.staff.forEach(staff => {
                        const option = document.createElement('option');
                        option.value = staff.name;
                        option.textContent = staff.name;
                        operatorSelect.appendChild(option);
                    });
                } else {
                    console.error('获取营业员列表失败:', response.statusText);
                }
            } catch (error) {
                console.error('加载营业员列表出错:', error);
            }
        }

        // 获取订单状态对应的CSS类
        function getOrderStatusClass(status) {
            switch(status) {
                case '门店已分拣': return 'sorted';
                case '送至工厂中': return 'to-factory';
                case '入厂': return 'in-factory';
                case '工厂分拣': return 'factory-sorted';
                case '出厂': return 'out-factory';
                case '送至分店中': return 'to-branch';
                case '已送至分店': return 'at-branch';
                case '已上架': return 'on-shelf';
                case '配送中': return 'delivering';
                case '已取消': return 'cancelled';
                case '已退赔': return 'compensated';
                case '已自取': return 'self-picked';
                case '已配送': return 'delivered';
                default: return 'pending';
            }
        }

        // 显示订单详情弹窗
        function showOrderDetail(order) {
            const modal = document.getElementById('orderDetailModal');
            const modalContent = document.getElementById('modalContent');

            // 创建订单基本信息部分，增加了可编辑的订单状态和支付状态
            let content = `
                <div class="order-detail-status-form">
                    <div class="order-detail-status-controls">
                        <div class="status-form-group">
                            <label for="orderStatus">订单状态:</label>
                            <select id="orderStatus" class="status-select">
                                <option value="门店已分拣" ${order.status === '门店已分拣' ? 'selected' : ''}>门店已分拣</option>
                                <option value="送至工厂中" ${order.status === '送至工厂中' ? 'selected' : ''}>送至工厂中</option>
                                <option value="入厂" ${order.status === '入厂' ? 'selected' : ''}>入厂</option>
                                <option value="工厂分拣" ${order.status === '工厂分拣' ? 'selected' : ''}>工厂分拣</option>
                                <option value="出厂" ${order.status === '出厂' ? 'selected' : ''}>出厂</option>
                                <option value="送至分店中" ${order.status === '送至分店中' ? 'selected' : ''}>送至分店中</option>
                                <option value="已送至分店" ${order.status === '已送至分店' ? 'selected' : ''}>已送至分店</option>
                                <option value="已上架" ${order.status === '已上架' ? 'selected' : ''}>已上架</option>
                                <option value="配送中" ${order.status === '配送中' ? 'selected' : ''}>配送中</option>
                                <option value="已取消" ${order.status === '已取消' ? 'selected' : ''}>已取消</option>
                                <option value="已退赔" ${order.status === '已退赔' ? 'selected' : ''}>已退赔</option>
                                <option value="已自取" ${order.status === '已自取' ? 'selected' : ''}>已自取</option>
                                <option value="已配送" ${order.status === '已配送' ? 'selected' : ''}>已配送</option>
                            </select>
                        </div>
                        <div class="status-form-group">
                            <label for="paymentStatus">支付状态:</label>
                            <select id="paymentStatus" class="status-select">
                                <option value="已付款" ${order.payment_status === '已付款' ? 'selected' : ''}>已付款</option>
                                <option value="未付款" ${order.payment_status === '未付款' ? 'selected' : ''}>未付款</option>
                            </select>
                        </div>
                        <div class="status-form-group">
                            <label for="statusRemarks">备注:</label>
                            <input type="text" id="statusRemarks" class="status-remarks" placeholder="状态变更备注信息">
                        </div>
                        <button id="updateOrderStatus" class="update-status-btn" data-order-id="${order.id}">更新状态</button>
                    </div>
                </div>

                <div class="modal-info-grid">
                    <div class="modal-info-item">
                        <div class="modal-info-label">客户姓名:</div>
                        <div>${order.customer_name || (order.customerInfo ? order.customerInfo.name : '未知')}</div>
                    </div>
                    <div class="modal-info-item">
                        <div class="modal-info-label">联系电话:</div>
                        <div>${order.customer_phone || (order.customerInfo ? order.customerInfo.phone : '未知')}</div>
                    </div>
                    <div class="modal-info-item">
                        <div class="modal-info-label">订单号:</div>
                        <div>${order.order_number}</div>
                    </div>
                    <div class="modal-info-item">
                        <div class="modal-info-label">创建时间:</div>
                        <div>${order.date}</div>
                    </div>
                    <div class="modal-info-item">
                        <div class="modal-info-label">总金额:</div>
                        <div>¥${order.total_amount.toFixed(2)}</div>
                    </div>
                    ${order.discount_amount > 0 ? `
                    <div class="modal-info-item">
                        <div class="modal-info-label">折扣金额:</div>
                        <div>¥${order.discount_amount.toFixed(2)}</div>
                    </div>
                    <div class="modal-info-item">
                        <div class="modal-info-label">实付金额:</div>
                        <div>¥${order.total_amount.toFixed(2)}</div>
                    </div>
                    ` : ''}
                    <div class="modal-info-item">
                        <div class="modal-info-label">支付方式:</div>
                        <div>${order.payment_method}</div>
                    </div>
                    <div class="modal-info-item">
                        <div class="modal-info-label">支付状态:</div>
                        <div><span class="payment-status ${order.payment_status === '已付款' ? 'paid' : 'unpaid'}">${order.payment_status}</span></div>
                    </div>
                    ${order.payment_status === '已付款' && order.payment_time ? `
                    <div class="modal-info-item">
                        <div class="modal-info-label">支付时间:</div>
                        <div>${order.payment_time}</div>
                    </div>
                    ` : ''}
                    <div class="modal-info-item">
                        <div class="modal-info-label">订单状态:</div>
                        <div><span class="order-status status-${getOrderStatusClass(order.status)}">${order.status}</span></div>
                    </div>
                    <div class="modal-info-item">
                        <div class="modal-info-label">营业员:</div>
                        <div>${order.operator}</div>
                    </div>
                    <div class="modal-info-item">
                        <div class="modal-info-label">地址:</div>
                        <div>${order.address || '无'}</div>
                    </div>
                    ${order.is_modified ? `
                    <div class="modal-info-item" style="grid-column: 1 / -1; background-color: #fff3cd; padding: 10px; border-radius: 5px; border-left: 4px solid #ffc107;">
                        <div class="modal-info-label" style="color: #856404; font-weight: bold;">修改状态:</div>
                        <div style="color: #856404;">
                            ✏️ 此订单已被修改 ${order.modification_count}次
                            ${order.last_modified_at ? `<br>最后修改时间: ${order.last_modified_at}` : ''}
                            ${order.last_modified_by ? `<br>最后修改人: ${order.last_modified_by}` : ''}
                        </div>
                    </div>
                    ` : ''}
                </div>

                <h4>衣物清单 (共 ${order.clothes.reduce((sum, item) => sum + (item.quantity || 1), 0)} 件)</h4>
            `;

            // 添加衣物信息表格
            content += `<div class="clothing-items-container">`;

            order.clothes.forEach((item, index) => {
                // 计算各服务项目价格
                let washingPrice = 0;
                let darningPrice = 0;
                let alteringPrice = 0;
                let otherPrice = 0;

                // 从特殊要求中获取各服务价格
                if (item.requirements) {
                    // 获取洗衣价格
                    if (item.requirements.wash && item.requirements.wash.price) {
                        washingPrice = parseFloat(item.requirements.wash.price);
                    } else if (item.services.includes('洗衣')) {
                        // 向后兼容：如果没有独立的洗衣价格，使用旧的计算方式
                        if (item.requirements.darn && item.requirements.darn.price) {
                            darningPrice = parseFloat(item.requirements.darn.price);
                        } else if (item.services.includes('织补')) {
                            darningPrice = getServicePrice('织补'); // 使用统一的价格配置
                        }

                        if (item.requirements.alter && item.requirements.alter.price) {
                            alteringPrice = parseFloat(item.requirements.alter.price);
                        } else if (item.services.includes('改衣')) {
                            alteringPrice = getServicePrice('改衣'); // 使用统一的价格配置
                        }

                        if (item.requirements.other && item.requirements.other.price) {
                            otherPrice = parseFloat(item.requirements.other.price);
                        } else if (item.services.includes('其他')) {
                            otherPrice = getServicePrice('其他'); // 使用统一的价格配置
                        }

                        washingPrice = item.price - darningPrice - alteringPrice - otherPrice;
                        washingPrice = Math.max(0, washingPrice);
                    }

                    // 获取织补价格
                    if (item.requirements.darn && item.requirements.darn.price) {
                        darningPrice = parseFloat(item.requirements.darn.price);
                    } else if (item.services.includes('织补')) {
                        darningPrice = getServicePrice('织补'); // 使用统一的价格配置
                    }

                    // 获取改衣价格
                    if (item.requirements.alter && item.requirements.alter.price) {
                        alteringPrice = parseFloat(item.requirements.alter.price);
                    } else if (item.services.includes('改衣')) {
                        alteringPrice = getServicePrice('改衣'); // 使用统一的价格配置
                    }

                    // 获取其他服务价格
                    if (item.requirements.other && item.requirements.other.price) {
                        otherPrice = parseFloat(item.requirements.other.price);
                    } else if (item.services.includes('其他')) {
                        otherPrice = getServicePrice('其他'); // 使用统一的价格配置
                    }
                } else {
                    // 如果没有特殊要求信息，使用统一的价格配置
                    if (item.services.includes('洗衣')) {
                        washingPrice = getServicePrice('洗衣'); // 使用统一的价格配置
                    }
                    if (item.services.includes('织补')) {
                        darningPrice = getServicePrice('织补'); // 使用统一的价格配置
                    }
                    if (item.services.includes('改衣')) {
                        alteringPrice = getServicePrice('改衣'); // 使用统一的价格配置
                    }
                    if (item.services.includes('其他')) {
                        otherPrice = getServicePrice('其他'); // 使用统一的价格配置
                    }
                }

                // 处理服务项目和价格
                let serviceWithPriceHTML = '';

                // 洗衣服务价格
                if (item.services.includes('洗衣')) {
                    serviceWithPriceHTML += `<span class="service-tag washing">洗衣 (¥${washingPrice.toFixed(2)})</span>`;
                }

                // 织补服务价格
                if (item.services.includes('织补')) {
                    serviceWithPriceHTML += `<span class="service-tag darning">织补 (¥${darningPrice.toFixed(2)})</span>`;
                }

                // 改衣服务价格
                if (item.services.includes('改衣')) {
                    serviceWithPriceHTML += `<span class="service-tag altering">改衣 (¥${alteringPrice.toFixed(2)})</span>`;
                }

                // 其他服务价格
                if (item.services.includes('其他')) {
                    serviceWithPriceHTML += `<span class="service-tag other">其他 (¥${otherPrice.toFixed(2)})</span>`;
                }

                // 其他服务（没有专门价格的服务，如加急等）
                item.services.forEach(service => {
                    if (!['洗衣', '织补', '改衣', '其他'].includes(service)) {
                        serviceWithPriceHTML += `<span class="service-tag other">${service}</span>`;
                    }
                });

                // 处理特殊要求内容
                let requirementsHTML = '';
                if (item.requirements) {
                    if (item.requirements.darn && item.requirements.darn.text) {
                        requirementsHTML += `<div><strong>织补要求:</strong> ${item.requirements.darn.text}</div>`;
                    }
                    if (item.requirements.alter && item.requirements.alter.text) {
                        requirementsHTML += `<div><strong>改衣要求:</strong> ${item.requirements.alter.text}</div>`;
                    }
                    if (item.requirements.other && item.requirements.other.text) {
                        requirementsHTML += `<div><strong>其他服务要求:</strong> ${item.requirements.other.text}</div>`;
                    }
                }

                // 为每件衣物创建卡片式展示
                content += `
                    <div class="clothing-item-card" data-item-id="${item.id}" data-order-id="${order.id}">
                        <div class="clothing-item-info">
                            <div class="clothing-item-header">
                                <h5>${item.name} (${item.color || '无'}) × ${item.quantity || 1}
                                    <button class="edit-item-btn" data-index="${index}">编辑</button>
                                </h5>
                                <span class="clothing-item-price">合计: ¥${item.price.toFixed(2)}</span>
                            </div>
                            <div class="clothing-item-services">
                                ${serviceWithPriceHTML}
                            </div>
                            <div class="clothing-item-remarks">
                                ${item.flaw ? `<div><strong>瑕疵:</strong> ${item.flaw}</div>` : ''}
                                <div><strong>备注:</strong> ${item.remarks || '无'}</div>
                                ${requirementsHTML}
                            </div>
                        </div>
                        <div class="clothing-item-photos">
                            ${item.photos && item.photos.length > 0 ?
                                item.photos.map(photo =>
                                    `<div class="clothing-photo-container">
                                        <img src="${photo}" class="clothing-photo" alt="衣物照片" data-image-src="${photo}">
                                    </div>`
                                ).join('') :
                                '<div class="no-photo">无照片</div>'
                            }
                        </div>
                    </div>
                `;
            });

            content += `</div>`;

            // 添加打印按钮
            content += `
                <div class="order-detail-buttons">
                    <button id="printReceiptBtn" class="action-button">小票打印</button>
                    <button id="printLabelBtn" class="action-button">水洗唛打印</button>
                </div>
            `;

            // 更新弹窗内容
            modalContent.innerHTML = content;

            // 添加小票打印按钮事件
            document.getElementById('printReceiptBtn').addEventListener('click', function() {
                console.log("打印小票按钮被点击, 订单信息:", order);
                // 确保有有效的订单ID
                const orderId = order.id || order.order_id;
                if (!orderId) {
                    console.error("订单数据中找不到有效的ID:", order);
                    alert("无法获取订单ID，打印失败");
                    return;
                }

                // 关闭当前模态框，避免多个打印内容同时存在
                document.getElementById('orderDetailModal').style.display = 'none';

                // 执行打印
                printReceipt(orderId, showLoading);
            });

            // 添加水洗唛打印按钮事件
            document.getElementById('printLabelBtn').addEventListener('click', function() {
                // 关闭当前模态框，避免多个打印内容同时存在
                document.getElementById('orderDetailModal').style.display = 'none';

                // 执行打印 - 使用直接打印模式，不显示UI
                printLabel(order.id, showLoading, 'direct');
            });

            // 添加更新订单状态按钮事件
            document.getElementById('updateOrderStatus').addEventListener('click', function() {
                const orderId = this.getAttribute('data-order-id');

                // 更明确的选择器，确保获取当前模态框中的元素
                const orderStatusSelect = document.querySelector('#orderDetailModal #orderStatus');
                const paymentStatusSelect = document.querySelector('#orderDetailModal #paymentStatus');
                const remarksInput = document.querySelector('#orderDetailModal #statusRemarks');

                // 调试输出选择器和元素
                console.log("状态选择器:", orderStatusSelect);
                console.log("支付状态选择器:", paymentStatusSelect);

                // 直接从DOM元素获取值
                const newStatus = orderStatusSelect ? orderStatusSelect.value : '';
                const newPaymentStatus = paymentStatusSelect ? paymentStatusSelect.value : '';
                const remarks = remarksInput ? remarksInput.value : '';

                // 调试日志
                console.log("选择的状态值:", {
                    orderId: orderId,
                    newStatus: newStatus,
                    newPaymentStatus: newPaymentStatus,
                    remarks: remarks
                });

                // 添加状态值验证
                if (!newStatus && !newPaymentStatus) {
                    alert('错误：未能获取订单状态或支付状态，请重新选择');
                    return;
                }

                // 先进行状态变更验证
                if (order.status !== newStatus) {
                    if (!confirm(`确定要将订单状态从 "${order.status}" 更改为 "${newStatus}" 吗?`)) {
                        return;
                    }

                    // 增加状态值打印，帮助调试
                    console.log('订单状态更新信息:', {
                        orderId: orderId,
                        oldStatus: order.status,
                        newStatus: newStatus,
                        paymentStatus: newPaymentStatus,
                        remarks: remarks
                    });
                }

                updateOrderStatus(orderId, newStatus, newPaymentStatus, remarks);
            });

            // 添加编辑衣物信息按钮事件
            document.querySelectorAll('.edit-item-btn').forEach((button, index) => {
                button.addEventListener('click', function() {
                    const itemData = order.clothes[index];
                    openEditItemModal(itemData, order.id);
                });
            });

            // 显示弹窗
            modal.style.display = 'block';
        }

        // 打开编辑衣物信息模态框
        function openEditItemModal(item, orderId) {
            const modal = document.getElementById('editItemModal');

            // 填充表单数据
            document.getElementById('editItemId').value = item.id;
            document.getElementById('editOrderId').value = orderId;
            document.getElementById('editItemName').value = item.name;
            document.getElementById('editItemColor').value = item.color || '';

            // 计算各服务项目价格（基于服务类型，不再使用基础价格概念）
            let washingPrice = 0;
            let darningPrice = 0;
            let alteringPrice = 0;
            let otherPrice = 0;

            // 从特殊要求中获取各服务价格，如果没有则使用默认价格
            if (item.requirements) {
                // 获取洗衣价格
                if (item.requirements.wash && item.requirements.wash.price) {
                    washingPrice = parseFloat(item.requirements.wash.price);
                } else if (item.services.includes('洗衣')) {
                    washingPrice = getServicePrice('洗衣');
                }

                // 获取织补价格
                if (item.requirements.darn && item.requirements.darn.price) {
                    darningPrice = parseFloat(item.requirements.darn.price);
                } else if (item.services.includes('织补')) {
                    darningPrice = getServicePrice('织补');
                }

                // 获取改衣价格
                if (item.requirements.alter && item.requirements.alter.price) {
                    alteringPrice = parseFloat(item.requirements.alter.price);
                } else if (item.services.includes('改衣')) {
                    alteringPrice = getServicePrice('改衣');
                }

                // 获取其他服务价格
                if (item.requirements.other && item.requirements.other.price) {
                    otherPrice = parseFloat(item.requirements.other.price);
                } else if (item.services.includes('其他')) {
                    otherPrice = getServicePrice('其他');
                }
            } else {
                // 如果没有特殊要求信息，根据服务类型使用默认价格
                if (item.services.includes('洗衣')) {
                    washingPrice = getServicePrice('洗衣');
                }
                if (item.services.includes('织补')) {
                    darningPrice = getServicePrice('织补');
                }
                if (item.services.includes('改衣')) {
                    alteringPrice = getServicePrice('改衣');
                }
                if (item.services.includes('其他')) {
                    otherPrice = getServicePrice('其他');
                }
            }

            // 向后兼容：如果所有服务价格都为0，但item.price有值，说明是旧数据
            // 尝试从总价格中推算洗衣价格
            if (washingPrice === 0 && darningPrice === 0 && alteringPrice === 0 && otherPrice === 0 && item.price > 0) {
                console.log('检测到旧数据格式，尝试向后兼容处理');
                if (item.services.includes('洗衣')) {
                    washingPrice = item.price; // 将总价格作为洗衣价格
                }
            }

            // 设置洗衣价格输入框
            document.getElementById('editItemPrice').value = washingPrice.toFixed(2);
            document.getElementById('editItemRemarks').value = item.remarks || '';
            document.getElementById('editItemFlaw').value = item.flaw || '';

            // 初始化照片显示
            initEditPhotos(item.photos || []);

            // 清空所有服务复选框
            document.getElementById('serviceWashing').checked = false;
            document.getElementById('serviceDarning').checked = false;
            document.getElementById('serviceAltering').checked = false;
            document.getElementById('serviceOther').checked = false;
            document.getElementById('serviceUrgent').checked = false;

            // 设置服务类型复选框
            if (item.services) {
                item.services.forEach(service => {
                    if (service === '洗衣') document.getElementById('serviceWashing').checked = true;
                    if (service === '织补') document.getElementById('serviceDarning').checked = true;
                    if (service === '改衣') document.getElementById('serviceAltering').checked = true;
                    if (service === '其他') document.getElementById('serviceOther').checked = true;
                    if (service === '加急') document.getElementById('serviceUrgent').checked = true;
                });
            }

            // 处理织补要求
            if (item.requirements && item.requirements.darn) {
                document.getElementById('editDarningRequirement').value = item.requirements.darn.text || '';
                document.getElementById('editDarningPrice').value = item.requirements.darn.price || getServicePrice('织补');
                document.getElementById('darningSection').style.display = 'block';
            } else {
                document.getElementById('editDarningRequirement').value = '';
                document.getElementById('editDarningPrice').value = getServicePrice('织补');
                document.getElementById('darningSection').style.display = item.services.includes('织补') ? 'block' : 'none';
            }

            // 处理改衣要求
            if (item.requirements && item.requirements.alter) {
                document.getElementById('editAlteringRequirement').value = item.requirements.alter.text || '';
                document.getElementById('editAlteringPrice').value = item.requirements.alter.price || getServicePrice('改衣');
                document.getElementById('alteringSection').style.display = 'block';
            } else {
                document.getElementById('editAlteringRequirement').value = '';
                document.getElementById('editAlteringPrice').value = getServicePrice('改衣');
                document.getElementById('alteringSection').style.display = item.services.includes('改衣') ? 'block' : 'none';
            }

            // 处理其他服务要求
            if (item.requirements && item.requirements.other) {
                document.getElementById('editOtherRequirement').value = item.requirements.other.text || '';
                document.getElementById('editOtherPrice').value = item.requirements.other.price || getServicePrice('其他');
                document.getElementById('otherSection').style.display = 'block';
            } else {
                document.getElementById('editOtherRequirement').value = '';
                document.getElementById('editOtherPrice').value = getServicePrice('其他');
                document.getElementById('otherSection').style.display = item.services.includes('其他') ? 'block' : 'none';
            }

            // 显示模态框
            modal.style.display = 'block';
        }

        // 保存衣物信息编辑
        async function saveItemEdit() {
            try {
                showLoading(true);

                const itemId = document.getElementById('editItemId').value;
                const orderId = document.getElementById('editOrderId').value;

                // 收集服务类型
                const services = [];
                if (document.getElementById('serviceWashing').checked) services.push('洗衣');
                if (document.getElementById('serviceDarning').checked) services.push('织补');
                if (document.getElementById('serviceAltering').checked) services.push('改衣');
                if (document.getElementById('serviceOther').checked) services.push('其他');
                if (document.getElementById('serviceUrgent').checked) services.push('加急');

                // 收集特殊要求
                const requirements = {};

                // 收集各服务价格（基于服务类型，不再使用基础价格概念）
                let washingPrice = 0;
                let darningPrice = 0;
                let alteringPrice = 0;
                let otherPrice = 0;

                // 洗衣服务价格
                if (document.getElementById('serviceWashing').checked) {
                    washingPrice = parseFloat(document.getElementById('editItemPrice').value);
                    requirements.wash = {
                        text: '', // 洗衣通常没有特殊要求文本
                        price: washingPrice
                    };
                }

                // 织补服务价格
                if (document.getElementById('serviceDarning').checked) {
                    darningPrice = parseFloat(document.getElementById('editDarningPrice').value);
                    requirements.darn = {
                        text: document.getElementById('editDarningRequirement').value,
                        price: darningPrice
                    };
                }

                // 改衣服务价格
                if (document.getElementById('serviceAltering').checked) {
                    alteringPrice = parseFloat(document.getElementById('editAlteringPrice').value);
                    requirements.alter = {
                        text: document.getElementById('editAlteringRequirement').value,
                        price: alteringPrice
                    };
                }

                // 其他服务价格
                if (document.getElementById('serviceOther').checked) {
                    otherPrice = parseFloat(document.getElementById('editOtherPrice').value);
                    requirements.other = {
                        text: document.getElementById('editOtherRequirement').value,
                        price: otherPrice
                    };
                }

                // 计算总价 = 洗衣价格 + 织补价格 + 改衣价格 + 其他服务价格
                const totalPrice = washingPrice + darningPrice + alteringPrice + otherPrice;

                // 收集照片数据
                const photos = [];
                document.querySelectorAll('#editPhotoThumbnails .edit-photo-thumbnail').forEach(img => {
                    photos.push(img.dataset.fullImage);
                });

                // 构建请求数据
                const itemData = {
                    id: parseInt(itemId),
                    order_id: parseInt(orderId),
                    name: document.getElementById('editItemName').value,
                    color: document.getElementById('editItemColor').value,
                    price: totalPrice, // 使用计算后的总价
                    remarks: document.getElementById('editItemRemarks').value,
                    flaw: document.getElementById('editItemFlaw').value,
                    services: services,
                    requirements: requirements,
                    photos: photos
                };

                // 发送请求
                const response = await fetch('/update_clothing_item', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(itemData)
                });

                if (!response.ok) {
                    throw new Error('更新衣物信息失败');
                }

                const result = await response.json();

                if (result.success) {
                    alert('衣物信息更新成功');
                    // 关闭编辑模态框
                    document.getElementById('editItemModal').style.display = 'none';
                    // 刷新订单详情
                    fetchCustomerHistory(paginationData.currentPage);
                    // 如果订单详情模态框是打开的，关闭它，后续会重新打开
                    document.getElementById('orderDetailModal').style.display = 'none';
                } else {
                    throw new Error(result.error || '更新衣物信息失败');
                }

            } catch (error) {
                console.error('更新衣物信息错误:', error);
                alert('更新衣物信息失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 注意：printReceipt函数已被移除，所有打印小票功能现在使用executePrintReceipt函数



        // 这些函数已移至共享的 print-functions.js 文件中

        // 显示放大的图片
        function showEnlargedImage(imageSrc) {
            const modal = document.getElementById('imageViewerModal');
            const enlargedImage = document.getElementById('enlargedImage');

            enlargedImage.src = imageSrc;
            modal.style.display = 'block';
        }

        // 更新订单状态函数
        async function updateOrderStatus(orderId, status, paymentStatus, remarks) {
            try {
                showLoading(true);

                // 调试日志
                console.log("发送更新请求:", {
                    orderId: orderId,
                    status: status,
                    payment_status: paymentStatus,
                    remarks: remarks
                });

                const response = await fetch('/update_order_status', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        order_id: orderId,
                        status: status || null,  // 修改：如果status为空字符串则发送null
                        payment_status: paymentStatus || null,  // 修改：如果paymentStatus为空字符串则发送null
                        remarks: remarks || ''
                    })
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || '更新订单状态失败');
                }

                const result = await response.json();

                if (result.success) {
                    alert('订单状态更新成功');
                    // 重新加载页面或更新当前展示的数据
                    fetchCustomerHistory(paginationData.currentPage);
                    document.getElementById('orderDetailModal').style.display = 'none';
                } else {
                    throw new Error(result.error || '更新订单状态失败');
                }

            } catch (error) {
                console.error('更新订单状态错误:', error);
                alert('更新订单状态失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        function showLoading(isLoading) {
            const loadingElement = document.getElementById('loadingOverlay');
            if (isLoading) {
                loadingElement.style.display = 'flex';
            } else {
                loadingElement.style.display = 'none';
            }
        }

        function showError(message) {
            const errorElement = document.getElementById('errorMessageContainer');
            if (!message) {
                errorElement.style.display = 'none';
                return;
            }

            errorElement.querySelector('p').textContent = message;
            errorElement.style.display = 'block';
        }

        // 生成小票HTML内容的函数已移至共享的 print-functions.js 文件中

        // 初始化编辑照片显示
        function initEditPhotos(photos) {
            const thumbnailsContainer = document.getElementById('editPhotoThumbnails');
            thumbnailsContainer.innerHTML = '';

            photos.forEach((photo, index) => {
                addEditPhotoThumbnail(photo);
            });
        }

        // 添加编辑照片缩略图
        function addEditPhotoThumbnail(photoSrc) {
            const thumbnailsContainer = document.getElementById('editPhotoThumbnails');

            const thumbnailContainer = document.createElement('div');
            thumbnailContainer.className = 'edit-photo-thumbnail-container';

            const thumbnail = document.createElement('img');
            thumbnail.className = 'edit-photo-thumbnail';
            thumbnail.src = photoSrc;
            thumbnail.dataset.fullImage = photoSrc;

            const removeBtn = document.createElement('button');
            removeBtn.className = 'edit-remove-photo';
            removeBtn.innerHTML = '×';
            removeBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                thumbnailContainer.remove();
            });

            // 点击缩略图查看大图
            thumbnail.addEventListener('click', function() {
                showEnlargedImage(this.dataset.fullImage);
            });

            thumbnailContainer.appendChild(thumbnail);
            thumbnailContainer.appendChild(removeBtn);
            thumbnailsContainer.appendChild(thumbnailContainer);
        }

        // 全局变量
        let cameraStream = null;
        let currentEditPhotoItem = null;

        // 页面加载完成后的事件绑定
        document.addEventListener('DOMContentLoaded', function() {
            // 绑定添加照片按钮事件
            document.getElementById('addEditPhotoBtn').addEventListener('click', function() {
                openEditCameraModal();
            });

            // 绑定摄像头模态框事件
            setupCameraModalEvents();
        });

        // 打开编辑摄像头模态框
        function openEditCameraModal() {
            const modal = document.getElementById('cameraModal');
            const cameraFeed = document.getElementById('modalCameraFeed');

            // 显示模态框
            modal.style.display = 'block';

            // 获取摄像头权限
            navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: 'environment', // 优先使用后置摄像头
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                },
                audio: false
            })
            .then(stream => {
                cameraStream = stream;
                cameraFeed.srcObject = stream;
            })
            .catch(error => {
                console.error('获取摄像头失败:', error);
                modal.style.display = 'none';
                alert('无法访问摄像头，请检查权限设置');
            });
        }

        // 关闭摄像头模态框
        function closeEditCameraModal() {
            const modal = document.getElementById('cameraModal');

            // 停止摄像头流
            if (cameraStream) {
                cameraStream.getTracks().forEach(track => track.stop());
                cameraStream = null;
            }

            // 隐藏模态框
            modal.style.display = 'none';
        }

        // 设置摄像头模态框事件
        function setupCameraModalEvents() {
            const modal = document.getElementById('cameraModal');
            const closeBtn = modal.querySelector('.close-modal');
            const takePhotoBtn = document.getElementById('takePhotoBtn');
            const cancelBtn = document.getElementById('cancelBtn');
            const cameraFeed = document.getElementById('modalCameraFeed');
            const photoCanvas = document.getElementById('photoCanvas');

            // 关闭按钮事件
            if (closeBtn) {
                closeBtn.addEventListener('click', closeEditCameraModal);
            }

            // 取消按钮事件
            if (cancelBtn) {
                cancelBtn.addEventListener('click', closeEditCameraModal);
            }

            // 拍照按钮事件
            if (takePhotoBtn) {
                takePhotoBtn.addEventListener('click', async function() {
                    if (!cameraStream) return;

                    try {
                        // 设置画布尺寸与视频流相同
                        const width = cameraFeed.videoWidth;
                        const height = cameraFeed.videoHeight;
                        photoCanvas.width = width;
                        photoCanvas.height = height;

                        // 在画布上绘制当前视频帧
                        const context = photoCanvas.getContext('2d');
                        context.drawImage(cameraFeed, 0, 0, width, height);

                        // 获取图像数据
                        const imageData = photoCanvas.toDataURL('image/jpeg', 0.8);

                        // 使用ImageCompressor库压缩图像
                        let compressedImage;
                        if (window.ImageCompressor && window.ImageCompressor.compress) {
                            const deviceConfig = window.ImageCompressor.getDeviceOptimizedConfig();
                            compressedImage = await window.ImageCompressor.compress(imageData, deviceConfig);
                        } else {
                            // 如果压缩库不可用，使用原始图像
                            console.warn("图片压缩库不可用，使用原始图像");
                            compressedImage = imageData;
                        }

                        console.log("图片处理成功，准备添加到编辑界面");

                        // 添加照片到编辑界面
                        addEditPhotoThumbnail(compressedImage);

                        // 关闭摄像头
                        closeEditCameraModal();
                    } catch (error) {
                        console.error("拍照失败:", error);
                        alert("拍照失败，请重试");
                    }
                });
            }

            // 点击模态框外部关闭
            window.addEventListener('click', function(event) {
                if (event.target === modal) {
                    closeEditCameraModal();
                }
            });
        }

    </script>
</body>
</html>